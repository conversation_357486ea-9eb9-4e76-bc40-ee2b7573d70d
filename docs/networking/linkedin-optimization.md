# LinkedIn Optimization

_Last updated: 2025‑05‑28_

## Snapshot
This file tracks ongoing tweaks to <PERSON>’s LinkedIn profile so it aligns with his goals:  
*Grow an audience of early‑career Java developers, showcase Bikeana, and highlight upcoming JPMorgan work.*

---

## 5 High‑Impact Improvements (≈1 hour total)

1. **Rewrite the headline for audience + outcome**  
   `Helping early‑career Java devs ship faster | Co‑lead @ Charlotte JUG | Incoming Spring APIs @ JPMorgan`

2. **Add an eye‑catching banner**  
   1584×396px image with Bikeana logo, tagline “Building in public • HTMX ✦ Quarkus”.

3. **Punch‑up the About section**  
   Three‑part story: **Who you help**, **what you’re building**, **social proof**. Finish with CTA “Connect for weekly Java‑in‑Production tips.”

4. **Populate the Featured section**  
   * Best‑performing X thread (~8K views)  
   * YouTube playlist: HTMX + Quarkus demo  
   * Key blog post on parra.dev (mailing‑list funnel)

5. **Pin Bikeana progress posts**  
   Cross‑post each #BuildInPublic clip; pin the latest in Activity → Posts.

---

## Nice‑to‑Haves (Next 30 Days)

- Add JPMorgan start date on 2025‑06‑09 with bullet points (AWS, Spring Boot APIs).  
- Create “Co‑lead, Charlotte Java User Group” entry under Volunteer Experience.  
- Obtain two fresh recommendations (peer & manager from Duke).  
- Re‑order Skills so **HTMX, Quarkus, AWS Lambda** sit in top three.  
- Enable **Creator Mode** with hashtags: #springboot #htmx #buildinpublic #java #aws.

---

## Posting Cadence

* **Tue–Thu, 11 a.m. ET** — mirrors Twitter’s coffee‑break spike.  
* Structure posts as **Hook → Context → Resource → CTA**; 120–180 words with a single emoji up top.

---
